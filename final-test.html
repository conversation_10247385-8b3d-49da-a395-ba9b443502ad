<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final YouTube Player Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-section.success {
            border-color: #4caf50;
            background: #e8f5e9;
        }
        .test-section.error {
            border-color: #f44336;
            background: #ffebee;
        }
        button {
            margin: 5px;
            padding: 12px 20px;
            cursor: pointer;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        
        .player-test {
            width: 100%;
            height: 400px;
            border: 2px solid #333;
            margin: 20px 0;
        }
        
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🎬 Final YouTube Player Test</h1>
    <p>This test will verify that the YouTube player works correctly in the main application.</p>
    
    <div class="test-section" id="step1">
        <h3>Step 1: Setup Test Data</h3>
        <button onclick="setupTestData()">Create Sample Playlist</button>
        <div class="status" id="step1Status">Click button to setup test data</div>
    </div>
    
    <div class="test-section" id="step2">
        <h3>Step 2: Initialize Application</h3>
        <button onclick="initializeApp()">Initialize App</button>
        <div class="status" id="step2Status">Setup test data first</div>
    </div>
    
    <div class="test-section" id="step3">
        <h3>Step 3: Test Player Container</h3>
        <div class="player-test" id="testPlayerContainer">
            <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666;">
                Test Player Container
            </div>
        </div>
        <button onclick="testPlayerContainer()">Test Player Container</button>
        <div class="status" id="step3Status">Initialize app first</div>
    </div>
    
    <div class="test-section" id="step4">
        <h3>Step 4: Test Video Selection</h3>
        <button onclick="testVideoSelection()">Select Test Video</button>
        <div class="status" id="step4Status">Complete previous steps first</div>
    </div>
    
    <div class="test-section" id="step5">
        <h3>Step 5: Final Verification</h3>
        <button onclick="finalVerification()">Verify Everything Works</button>
        <button onclick="openMainApp()">Open Main App</button>
        <div class="status" id="step5Status">Complete all previous steps</div>
    </div>
    
    <div class="test-section">
        <h3>Debug Information</h3>
        <button onclick="showDebugInfo()">Show Debug Info</button>
        <pre id="debugInfo">Click button to show debug information</pre>
    </div>

    <script src="config.js"></script>
    <script src="storage.js"></script>
    <script src="api.js"></script>
    <script src="app.js"></script>
    
    <script>
        function updateStatus(stepId, message, type = 'info') {
            const statusElement = document.getElementById(stepId + 'Status');
            const sectionElement = document.getElementById(stepId);
            
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
            
            if (type === 'success') {
                sectionElement.className = 'test-section success';
            } else if (type === 'error') {
                sectionElement.className = 'test-section error';
            } else {
                sectionElement.className = 'test-section';
            }
        }
        
        function setupTestData() {
            try {
                const storage = window.storageManager;
                
                const samplePlaylist = {
                    id: 'PLtest123',
                    title: 'Test Playlist for Player',
                    description: 'A test playlist to verify YouTube player functionality',
                    channelTitle: 'Test Channel',
                    thumbnail: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
                    videoCount: 2,
                    totalDuration: 400,
                    createdAt: new Date().toISOString(),
                    videos: [
                        {
                            id: 'dQw4w9WgXcQ',
                            title: 'Rick Astley - Never Gonna Give You Up',
                            description: 'Test video 1',
                            thumbnail: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
                            duration: 212,
                            position: 0,
                            currentTime: 0,
                            completionPercentage: 0,
                            completed: false,
                            lastWatched: null
                        },
                        {
                            id: 'oHg5SJYRHA0',
                            title: 'RickRoll\'d',
                            description: 'Test video 2',
                            thumbnail: 'https://i.ytimg.com/vi/oHg5SJYRHA0/maxresdefault.jpg',
                            duration: 188,
                            position: 1,
                            currentTime: 0,
                            completionPercentage: 0,
                            completed: false,
                            lastWatched: null
                        }
                    ]
                };
                
                storage.savePlaylist(samplePlaylist);
                storage.setCurrentPlaylist(samplePlaylist);
                
                updateStatus('step1', '✅ Test data created successfully!', 'success');
                console.log('✅ Test data setup complete');
                
            } catch (error) {
                updateStatus('step1', `❌ Failed to setup test data: ${error.message}`, 'error');
                console.error('❌ Test data setup failed:', error);
            }
        }
        
        async function initializeApp() {
            try {
                if (!window.playlistTracker) {
                    throw new Error('PlaylistTracker not found');
                }
                
                // Force re-initialization
                await window.playlistTracker.init();
                
                updateStatus('step2', '✅ Application initialized successfully!', 'success');
                console.log('✅ App initialization complete');
                
            } catch (error) {
                updateStatus('step2', `❌ Failed to initialize app: ${error.message}`, 'error');
                console.error('❌ App initialization failed:', error);
            }
        }
        
        async function testPlayerContainer() {
            try {
                const container = document.getElementById('testPlayerContainer');
                container.innerHTML = '<div id="test-youtube-player"></div>';
                
                const testPlayer = new YouTubePlayer('test-youtube-player');
                await testPlayer.init();
                
                updateStatus('step3', '✅ Player container test passed!', 'success');
                console.log('✅ Player container test complete');
                
                // Clean up
                setTimeout(() => {
                    testPlayer.destroy();
                }, 2000);
                
            } catch (error) {
                updateStatus('step3', `❌ Player container test failed: ${error.message}`, 'error');
                console.error('❌ Player container test failed:', error);
            }
        }
        
        async function testVideoSelection() {
            try {
                const app = window.playlistTracker;
                
                if (!app.state.currentPlaylist || !app.state.currentPlaylist.videos.length) {
                    throw new Error('No playlist or videos available');
                }
                
                const testVideo = app.state.currentPlaylist.videos[0];
                await app.selectVideo(testVideo);
                
                // Check if player was created
                if (app.youtubePlayer) {
                    updateStatus('step4', '✅ Video selection test passed! Player created.', 'success');
                } else {
                    updateStatus('step4', '⚠️ Video selected but no player created (demo mode)', 'warning');
                }
                
                console.log('✅ Video selection test complete');
                
            } catch (error) {
                updateStatus('step4', `❌ Video selection test failed: ${error.message}`, 'error');
                console.error('❌ Video selection test failed:', error);
            }
        }
        
        function finalVerification() {
            try {
                const app = window.playlistTracker;
                const results = [];
                
                // Check API key
                results.push(`API Key: ${window.CONFIG?.YOUTUBE_API_KEY ? '✅ Present' : '❌ Missing'}`);
                
                // Check API initialization
                results.push(`API Initialized: ${app.isUsingRealAPI ? '✅ Yes' : '❌ No'}`);
                
                // Check player
                results.push(`YouTube Player: ${app.youtubePlayer ? '✅ Created' : '❌ Not Created'}`);
                
                // Check playlist
                results.push(`Current Playlist: ${app.state.currentPlaylist ? '✅ Loaded' : '❌ Not Loaded'}`);
                
                // Check current video
                results.push(`Current Video: ${app.state.currentVideo ? '✅ Selected' : '❌ None Selected'}`);
                
                const allGood = results.every(r => r.includes('✅'));
                
                if (allGood) {
                    updateStatus('step5', '🎉 All tests passed! YouTube player should work.', 'success');
                } else {
                    updateStatus('step5', '⚠️ Some issues found. Check debug info.', 'warning');
                }
                
                console.log('Final verification results:', results);
                
            } catch (error) {
                updateStatus('step5', `❌ Final verification failed: ${error.message}`, 'error');
                console.error('❌ Final verification failed:', error);
            }
        }
        
        function showDebugInfo() {
            const app = window.playlistTracker;
            const debugInfo = {
                config: {
                    hasConfig: !!window.CONFIG,
                    hasApiKey: !!(window.CONFIG?.YOUTUBE_API_KEY),
                    apiKey: window.CONFIG?.YOUTUBE_API_KEY ? 'PROVIDED' : 'MISSING'
                },
                app: {
                    exists: !!app,
                    isUsingRealAPI: app?.isUsingRealAPI,
                    hasYouTubePlayer: !!app?.youtubePlayer,
                    hasCurrentPlaylist: !!app?.state?.currentPlaylist,
                    hasCurrentVideo: !!app?.state?.currentVideo
                },
                storage: {
                    hasStorageManager: !!window.storageManager,
                    currentPlaylist: window.storageManager?.getCurrentPlaylist()?.title || 'None'
                },
                dom: {
                    hasVideoPlayerElement: !!document.getElementById('videoPlayer'),
                    hasMainContent: !!document.getElementById('mainContent')
                }
            };
            
            document.getElementById('debugInfo').textContent = JSON.stringify(debugInfo, null, 2);
        }
        
        function openMainApp() {
            window.open('index.html', '_blank');
        }
        
        // Auto-run on page load
        window.addEventListener('load', () => {
            console.log('🔄 Final test page loaded');
            showDebugInfo();
        });
    </script>
</body>
</html>

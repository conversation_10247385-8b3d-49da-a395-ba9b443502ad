// Configuration file for IsotopeAI - YT Tracker
// Replace 'YOUR_API_KEY_HERE' with your actual YouTube Data API v3 key

const CONFIG = {
    // YouTube Data API v3 Key
    // Get your key from: https://console.developers.google.com/
    YOUTUBE_API_KEY: 'AIzaSyAm2D555TnOgHYIxNJeTR_ZMAC21oy4LR0',

    // Application settings
    APP_NAME: 'IsotopeAI - YT Tracker',
    VERSION: '1.0.0',

    // Default settings
    DEFAULT_SETTINGS: {
        theme: 'auto',
        autoSave: true,
        notifications: true,
        autoMarkComplete: true
    },

    // API Configuration
    API_CONFIG: {
        baseURL: 'https://www.googleapis.com/youtube/v3',
        maxResults: 50,
        timeout: 10000
    },

    // Feature flags
    FEATURES: {
        realTimeSync: false,
        exportData: true,
        importData: true,
        keyboardShortcuts: true,
        notifications: true
    }
};

// Initialize the app with configuration
document.addEventListener('DOMContentLoaded', async () => {
    if (window.playlistTracker && CONFIG.YOUTUBE_API_KEY !== 'YOUR_API_KEY_HERE') {
        await window.playlistTracker.initializeYouTubeAPI(CONFIG.YOUTUBE_API_KEY);
    }
});

// Export configuration for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
} else {
    window.CONFIG = CONFIG;
}
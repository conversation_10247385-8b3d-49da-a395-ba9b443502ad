<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug YouTube Player</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .debug-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            margin: 5px;
            padding: 10px 15px;
            cursor: pointer;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            font-family: monospace;
        }
        .player-container {
            width: 100%;
            height: 400px;
            border: 2px solid #ccc;
            margin: 20px 0;
            position: relative;
        }
        #videoPlayer {
            width: 100%;
            height: 100%;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>Debug YouTube Player Initialization</h1>
    
    <div class="debug-section">
        <h3>1. Configuration Check</h3>
        <button onclick="checkConfig()">Check Config</button>
        <div class="status" id="configStatus">Not checked</div>
    </div>
    
    <div class="debug-section">
        <h3>2. API Initialization</h3>
        <button onclick="initAPI()">Initialize API</button>
        <div class="status" id="apiStatus">Not initialized</div>
    </div>
    
    <div class="debug-section">
        <h3>3. Player Container</h3>
        <button onclick="checkContainer()">Check Container</button>
        <div class="status" id="containerStatus">Not checked</div>
        <div class="player-container">
            <div id="videoPlayer">
                <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666;">
                    Player container ready
                </div>
            </div>
        </div>
    </div>
    
    <div class="debug-section">
        <h3>4. YouTube Player Initialization</h3>
        <button onclick="initPlayer()">Initialize Player</button>
        <button onclick="testVideo()">Load Test Video</button>
        <div class="status" id="playerStatus">Not initialized</div>
    </div>
    
    <div class="debug-section">
        <h3>5. Full App Test</h3>
        <button onclick="testFullApp()">Test Full App Flow</button>
        <div class="status" id="appStatus">Not tested</div>
    </div>
    
    <div class="debug-section">
        <h3>Console Log</h3>
        <button onclick="clearLog()">Clear Log</button>
        <div class="log" id="debugLog"></div>
    </div>

    <script src="config.js"></script>
    <script src="storage.js"></script>
    <script src="api.js"></script>
    <script src="app.js"></script>
    
    <script>
        let debugPlayer = null;
        
        function log(message) {
            const logDiv = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function updateStatus(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.style.background = isError ? '#ffebee' : '#e8f5e8';
            element.style.color = isError ? '#c62828' : '#2e7d32';
        }
        
        function checkConfig() {
            log('🔍 Checking configuration...');
            
            if (window.CONFIG) {
                log('✅ CONFIG object found');
                log(`API Key: ${window.CONFIG.YOUTUBE_API_KEY ? 'PROVIDED' : 'MISSING'}`);
                updateStatus('configStatus', `Config loaded. API Key: ${window.CONFIG.YOUTUBE_API_KEY ? 'PROVIDED' : 'MISSING'}`);
            } else {
                log('❌ CONFIG object not found');
                updateStatus('configStatus', 'CONFIG object not found', true);
            }
        }
        
        async function initAPI() {
            log('🔑 Initializing API...');
            
            if (!window.CONFIG || !window.CONFIG.YOUTUBE_API_KEY) {
                updateStatus('apiStatus', 'No API key available', true);
                return;
            }
            
            try {
                if (window.playlistTracker) {
                    await window.playlistTracker.initializeYouTubeAPI(window.CONFIG.YOUTUBE_API_KEY);
                    log('✅ API initialized via playlistTracker');
                    updateStatus('apiStatus', 'API initialized successfully');
                } else {
                    log('❌ playlistTracker not found');
                    updateStatus('apiStatus', 'playlistTracker not found', true);
                }
            } catch (error) {
                log(`❌ API initialization failed: ${error.message}`);
                updateStatus('apiStatus', `API initialization failed: ${error.message}`, true);
            }
        }
        
        function checkContainer() {
            log('📦 Checking player container...');
            
            const container = document.getElementById('videoPlayer');
            if (container) {
                log('✅ Player container found');
                log(`Container dimensions: ${container.offsetWidth}x${container.offsetHeight}`);
                updateStatus('containerStatus', `Container found (${container.offsetWidth}x${container.offsetHeight})`);
            } else {
                log('❌ Player container not found');
                updateStatus('containerStatus', 'Player container not found', true);
            }
        }
        
        async function initPlayer() {
            log('🎬 Initializing YouTube player...');
            
            try {
                const container = document.getElementById('videoPlayer');
                if (!container) {
                    throw new Error('Player container not found');
                }
                
                container.innerHTML = '<div id="youtube-player"></div>';
                
                debugPlayer = new YouTubePlayer('youtube-player');
                await debugPlayer.init();
                
                log('✅ YouTube player initialized successfully');
                updateStatus('playerStatus', 'Player initialized successfully');
                
                debugPlayer.onStateChange((state) => {
                    log(`🎵 Player state changed: ${state}`);
                });
                
            } catch (error) {
                log(`❌ Player initialization failed: ${error.message}`);
                updateStatus('playerStatus', `Player initialization failed: ${error.message}`, true);
            }
        }
        
        function testVideo() {
            log('🎥 Testing video load...');
            
            if (!debugPlayer) {
                log('❌ Player not initialized');
                return;
            }
            
            try {
                debugPlayer.loadVideo('dQw4w9WgXcQ');
                log('✅ Video load command sent');
            } catch (error) {
                log(`❌ Video load failed: ${error.message}`);
            }
        }
        
        async function testFullApp() {
            log('🚀 Testing full app flow...');
            
            try {
                // Check if app exists
                if (!window.playlistTracker) {
                    throw new Error('playlistTracker not found');
                }
                
                // Check if API is initialized
                if (!window.playlistTracker.isUsingRealAPI) {
                    log('⚠️ API not initialized, initializing now...');
                    await initAPI();
                }
                
                // Check if player exists
                if (!window.playlistTracker.youtubePlayer) {
                    log('⚠️ Player not found in app, this might be the issue');
                    updateStatus('appStatus', 'Player not found in main app - THIS IS THE ISSUE!', true);
                } else {
                    log('✅ Player found in main app');
                    updateStatus('appStatus', 'Full app test passed');
                }
                
            } catch (error) {
                log(`❌ Full app test failed: ${error.message}`);
                updateStatus('appStatus', `Full app test failed: ${error.message}`, true);
            }
        }
        
        function clearLog() {
            document.getElementById('debugLog').innerHTML = '';
        }
        
        // Auto-run basic checks
        window.addEventListener('load', () => {
            log('🔄 Page loaded, running basic checks...');
            checkConfig();
            setTimeout(checkContainer, 100);
        });
    </script>
</body>
</html>

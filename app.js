// IsotopeAI - YT Tracker Application

class PlaylistTracker {
    constructor() {
        this.state = {
            currentPlaylist: null,
            currentVideo: null,
            isPlaying: false,
            currentTime: 0,
            settings: {
                theme: 'auto',
                autoSave: true,
                notifications: true,
                autoMarkComplete: true,
                apiKey: null // Will be set when provided
            },
            progressStats: {
                totalVideos: 0,
                completedVideos: 0,
                inProgressVideos: 0,
                totalWatchTime: 0,
                totalDuration: 0,
                completionPercentage: 0,
                currentStreak: 0
            }
        };
        
        this.playbackTimer = null;
        this.lastUpdateTime = Date.now();
        this.youtubeAPI = new YouTubeAPI();
        this.youtubePlayer = null;
        this.isUsingRealAPI = false;
        this.storage = window.storageManager;
        
        // Ensure DOM is loaded before initializing
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                // Hide loading indicator immediately when DOM is ready
                const indicator = document.getElementById('loadingIndicator');
                if (indicator) {
                    indicator.classList.add('hidden');
                }
                this.init();
            });
        } else {
            // Hide loading indicator immediately if DOM is already ready
            const indicator = document.getElementById('loadingIndicator');
            if (indicator) {
                indicator.classList.add('hidden');
            }
            this.init();
        }
    }

    // Toast notification system
    showToast(title, message, type = 'info', duration = 5000) {
        const container = document.getElementById('toastContainer');
        if (!container) return;

        const toast = document.createElement('div');
        toast.className = `toast toast--${type}`;
        
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        toast.innerHTML = `
            <div class="toast-icon">
                <i class="${icons[type]}"></i>
            </div>
            <div class="toast-content">
                <div class="toast-title">${title}</div>
                <div class="toast-message">${message}</div>
            </div>
            <button class="toast-close">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Add close functionality
        const closeBtn = toast.querySelector('.toast-close');
        closeBtn.addEventListener('click', () => this.removeToast(toast));

        container.appendChild(toast);

        // Auto remove after duration
        if (duration > 0) {
            setTimeout(() => this.removeToast(toast), duration);
        }

        return toast;
    }

    removeToast(toast) {
        if (toast && toast.parentNode) {
            toast.classList.add('removing');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }
    }

    // Loading indicator
    showLoading(message = 'Loading...') {
        const indicator = document.getElementById('loadingIndicator');
        if (indicator) {
            const messageEl = indicator.querySelector('p');
            if (messageEl) messageEl.textContent = message;
            indicator.classList.remove('hidden');
        }
    }

    hideLoading() {
        const indicator = document.getElementById('loadingIndicator');
        if (indicator) {
            indicator.classList.add('hidden');
            console.log('Loading indicator hidden');
        } else {
            console.error('Loading indicator element not found');
        }
    }

    // Initialize YouTube API if key is provided
    async initializeYouTubeAPI(apiKey) {
        console.log('🔑 Initializing YouTube API with key:', apiKey ? 'PROVIDED' : 'MISSING');
        if (apiKey) {
            this.youtubeAPI.init(apiKey);
            this.isUsingRealAPI = true;
            this.state.settings.apiKey = apiKey;
            this.showToast('API Connected', 'YouTube API initialized successfully', 'success');
            console.log('🔑 YouTube API initialized with provided key');

            // Initialize YouTube player immediately when API is ready
            if (!this.youtubePlayer) {
                console.log('🔑 Initializing YouTube player after API setup...');
                const success = await this.initializeYouTubePlayer();
                if (success) {
                    console.log('🔑 YouTube player ready for use');
                } else {
                    console.log('🔑 YouTube player initialization failed during API setup');
                }
            } else {
                console.log('🔑 YouTube player already exists');
            }
        } else {
            console.log('🔑 No API key provided');
        }
    }

    // Initialize YouTube Player
    async initializeYouTubePlayer() {
        console.log('🎬 Starting YouTube player initialization...');
        try {
            const playerContainer = document.getElementById('videoPlayer');
            console.log('🎬 Player container found:', !!playerContainer);

            if (playerContainer) {
                // Create a div for the YouTube player
                console.log('🎬 Creating youtube-player div...');
                playerContainer.innerHTML = '<div id="youtube-player"></div>';

                console.log('🎬 Creating YouTubePlayer instance...');
                this.youtubePlayer = new YouTubePlayer('youtube-player');

                // Add timeout to prevent hanging indefinitely
                console.log('🎬 Initializing player with timeout...');
                const initPromise = this.youtubePlayer.init();
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('YouTube player initialization timeout')), 10000);
                });

                await Promise.race([initPromise, timeoutPromise]);

                // Set up player callbacks
                console.log('🎬 Setting up player callbacks...');
                this.youtubePlayer.onProgress((data) => {
                    this.handlePlayerProgress(data);
                });

                this.youtubePlayer.onStateChange((state) => {
                    this.handlePlayerStateChange(state);
                });

                console.log('🎬 YouTube player initialized successfully!');
                return true;
            } else {
                console.error('🎬 Player container not found!');
                return false;
            }
        } catch (error) {
            console.error('🎬 Failed to initialize YouTube player:', error);
            this.showToast('Player Error', 'Failed to initialize video player. Falling back to demo mode.', 'warning');

            // Fall back to simulated player and reset API flag
            this.youtubePlayer = null;
            this.isUsingRealAPI = false;

            // Update the UI to reflect the fallback
            if (this.state.currentVideo) {
                this.updateVideoPlayer();
            }

            return false;
        }
    }

    // Handle real YouTube player progress
    handlePlayerProgress(data) {
        if (this.state.currentVideo && data.videoId === this.state.currentVideo.id) {
            this.state.currentTime = data.currentTime;
            this.updateProgressBar();
            this.updateVideoProgress();
            
            if (this.state.settings.autoSave) {
                this.saveProgress();
            }
        }
    }

    // Handle YouTube player state changes
    handlePlayerStateChange(state) {
        const playPauseBtn = document.getElementById('playPauseBtn');
        
        switch (state) {
            case 'playing':
                this.state.isPlaying = true;
                if (playPauseBtn) playPauseBtn.innerHTML = '<i class="fas fa-pause"></i>';
                break;
            case 'paused':
                this.state.isPlaying = false;
                if (playPauseBtn) playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
                break;
            case 'ended':
                this.state.isPlaying = false;
                if (playPauseBtn) playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
                if (this.state.settings.autoMarkComplete) {
                    this.completeCurrentVideo();
                    this.selectNextVideo();
                }
                break;
        }
    }

    // This init method is replaced by the async version below

    bindEvents() {
        console.log('Binding events...');
        
        // URL Input and Validation
        const urlInput = document.getElementById('playlistUrlInput');
        const loadBtn = document.getElementById('loadPlaylistBtn');
        
        if (urlInput && loadBtn) {
            urlInput.addEventListener('input', (e) => this.validateURL(e));
            urlInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.loadPlaylist();
                }
            });
            loadBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.loadPlaylist();
            });
            console.log('URL input events bound');
        } else {
            console.error('URL input or load button not found');
        }

        // Settings Modal
        const settingsBtn = document.getElementById('settingsBtn');
        const closeSettingsBtn = document.getElementById('closeSettingsBtn');
        const modalOverlay = document.getElementById('modalOverlay');
        const saveSettingsBtn = document.getElementById('saveSettingsBtn');
        const resetSettingsBtn = document.getElementById('resetSettingsBtn');

        if (settingsBtn) {
            settingsBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.openSettings();
            });
            console.log('Settings button event bound');
        }

        if (closeSettingsBtn) {
            closeSettingsBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.closeSettings();
            });
        }

        if (modalOverlay) {
            modalOverlay.addEventListener('click', (e) => {
                if (e.target === modalOverlay) {
                    this.closeSettings();
                }
            });
        }

        if (saveSettingsBtn) {
            saveSettingsBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.saveSettingsFromModal();
            });
        }

        if (resetSettingsBtn) {
            resetSettingsBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.resetSettings();
            });
        }

        // Video Player Controls
        const playPauseBtn = document.getElementById('playPauseBtn');
        const markCompleteBtn = document.getElementById('markCompleteBtn');
        
        if (playPauseBtn) {
            playPauseBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.togglePlayback();
            });
        }

        if (markCompleteBtn) {
            markCompleteBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.markCurrentVideoComplete();
            });
        }
        
        // Progress Bar Interaction
        const progressBar = document.querySelector('.progress-bar');
        if (progressBar) {
            progressBar.addEventListener('click', (e) => this.seekToPosition(e));
        }

        // Video List Interaction
        const videoList = document.getElementById('videoList');
        if (videoList) {
            videoList.addEventListener('click', (e) => this.handleVideoSelection(e));
        }
        
        // Search and Filter
        const videoSearchInput = document.getElementById('videoSearchInput');
        if (videoSearchInput) {
            videoSearchInput.addEventListener('input', () => this.filterVideos());
        }

        const filterBtns = document.querySelectorAll('.filter-btn');
        filterBtns.forEach(btn => {
            btn.addEventListener('click', (e) => this.handleFilterChange(e));
        });


        // Settings Controls
        const themeSelect = document.getElementById('themeSelect');
        if (themeSelect) {
            themeSelect.addEventListener('change', (e) => this.handleThemeChange(e));
        }

        const exportDataBtn = document.getElementById('exportDataBtn');
        const importDataBtn = document.getElementById('importDataBtn');
        
        if (exportDataBtn) {
            exportDataBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.exportData();
            });
        }

        if (importDataBtn) {
            importDataBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.importData();
            });
        }

        // Keyboard Shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));
        
        console.log('All events bound successfully');
    }

    validateURL(event) {
        const url = event.target.value;
        const validation = document.getElementById('urlValidation');
        
        if (!validation) return false;
        
        if (!url) {
            validation.textContent = '';
            validation.className = 'url-validation';
            return false;
        }

        const playlistRegex = /(?:youtube\.com\/playlist\?list=|youtu\.be\/playlist\?list=)([a-zA-Z0-9_-]+)/;
        const isValid = playlistRegex.test(url);

        if (isValid) {
            validation.textContent = '✓ Valid YouTube playlist URL';
            validation.className = 'url-validation valid';
            return true;
        } else {
            validation.textContent = '✗ Please enter a valid YouTube playlist URL';
            validation.className = 'url-validation invalid';
            return false;
        }
    }

    async loadPlaylist() {
        console.log('Loading playlist...');
        
        const urlInput = document.getElementById('playlistUrlInput');
        const loadBtn = document.getElementById('loadPlaylistBtn');
        
        if (!urlInput || !loadBtn) {
            console.error('Required elements not found');
            return;
        }

        const url = urlInput.value.trim();

        // Validate URL first
        if (!this.validateURL({ target: { value: url } })) {
            this.showToast('Invalid URL', 'Please enter a valid YouTube playlist URL', 'error');
            return;
        }

        // Show loading state
        const originalText = loadBtn.innerHTML;
        loadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
        loadBtn.disabled = true;
        this.showLoading('Loading playlist data...');

        try {
            let playlistData;
            
            // Always use real YouTube API
            console.log('Loading playlist with YouTube API...');
            playlistData = await this.youtubeAPI.loadPlaylist(url);
            this.showToast('Playlist Loaded', `Successfully loaded "${playlistData.title}"`, 'success');
            
            // Add metadata
            playlistData.createdAt = new Date().toISOString();
            playlistData.lastAccessed = new Date().toISOString();
            
            this.state.currentPlaylist = playlistData;
            this.calculateProgressStats();
            
            console.log('Playlist loaded:', this.state.currentPlaylist);
            
            // Save to storage
            this.storage.setCurrentPlaylist(playlistData);
            
            // Hide upload section and show main content
            const uploadSection = document.getElementById('uploadSection');
            const mainContent = document.getElementById('mainContent');
            
            if (uploadSection && mainContent) {
                uploadSection.style.display = 'none';
                mainContent.style.display = 'grid';
                console.log('UI updated - main content shown');
            }
            
            // Ensure YouTube player is initialized if using real API
            if (!this.youtubePlayer && this.isUsingRealAPI) {
                console.log('YouTube player not found, attempting to initialize...');
                const success = await this.initializeYouTubePlayer();
                if (!success) {
                    console.log('YouTube player initialization failed, continuing with demo mode');
                }
            }
            
            // Load first video or resume from last watched
            const resumeVideo = this.state.currentPlaylist.videos.find(v => 
                v.completionPercentage > 0 && !v.completed
            ) || this.state.currentPlaylist.videos[0];
            
            if (resumeVideo) {
                this.selectVideo(resumeVideo);
            }
            
            // Update UI
            this.renderVideoList();
            this.updateProgressAnalytics();
            this.updateHeaderInfo();
            
            console.log('Playlist loading completed successfully');
            
        } catch (error) {
            console.error('Error loading playlist:', error);
            let errorMessage = 'Failed to load playlist. Please try again.';
            
            if (error.message.includes('API request failed')) {
                errorMessage = 'API request failed. Please check your API key and try again.';
            } else if (error.message.includes('not found')) {
                errorMessage = 'Playlist not found or is private. Please check the URL.';
            } else if (error.message.includes('quota')) {
                errorMessage = 'API quota exceeded. Please try again later.';
            }
            
            this.showToast('Loading Failed', errorMessage, 'error');
        } finally {
            loadBtn.innerHTML = originalText;
            loadBtn.disabled = false;
            this.hideLoading();
        }
    }



    async selectVideo(video) {
        console.log('🎯 Selecting video:', video.title);

        // Stop current playback
        if (this.state.isPlaying) {
            this.togglePlayback();
        }

        // Update state
        const previousVideo = this.state.currentVideo;
        this.state.currentVideo = video;
        this.state.currentTime = video.currentTime || 0;

        // Ensure YouTube player is initialized if using real API
        if (this.isUsingRealAPI && !this.youtubePlayer) {
            console.log('🎯 Player not found, initializing now...');
            const success = await this.initializeYouTubePlayer();
            if (!success) {
                console.log('🎯 Player initialization failed during video selection');
            }
        }

        // Load video in YouTube player if available
        if (this.youtubePlayer) {
            console.log('🎯 Loading video in YouTube player:', video.id);
            this.youtubePlayer.loadVideo(video.id, video.currentTime || 0);
        } else {
            console.log('🎯 No YouTube player available, using demo mode');
        }

        // Update UI
        this.updateVideoPlayer();
        this.updateVideoList();

        // Mark video as started if not already
        if (video.completionPercentage === 0) {
            video.lastWatched = new Date().toISOString();
        }

        // Show notification for video change
        if (previousVideo && previousVideo.id !== video.id) {
            this.showToast('Now Playing', video.title, 'info', 3000);
        }

        // Save progress
        if (this.state.settings.autoSave) {
            this.saveProgress();
        }
    }

    togglePlayback() {
        if (!this.state.currentVideo) return;

        if (this.youtubePlayer) {
            // Use real YouTube player
            if (this.state.isPlaying) {
                this.youtubePlayer.pause();
            } else {
                this.youtubePlayer.play();
            }
        } else {
            // Use simulated playback
            this.state.isPlaying = !this.state.isPlaying;
            const playPauseBtn = document.getElementById('playPauseBtn');
            
            if (playPauseBtn) {
                if (this.state.isPlaying) {
                    playPauseBtn.innerHTML = '<i class="fas fa-pause"></i>';
                    this.startPlaybackTimer();
                } else {
                    playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
                    this.stopPlaybackTimer();
                }
            }
        }

        console.log('Playback toggled:', this.state.isPlaying ? 'playing' : 'paused');
    }

    startPlaybackTimer() {
        this.lastUpdateTime = Date.now();
        this.playbackTimer = setInterval(() => {
            const now = Date.now();
            const elapsed = (now - this.lastUpdateTime) / 1000;
            this.lastUpdateTime = now;

            this.state.currentTime += elapsed;
            
            if (this.state.currentTime >= this.state.currentVideo.duration) {
                this.state.currentTime = this.state.currentVideo.duration;
                this.completeCurrentVideo();
                this.togglePlayback();
            }

            this.updateProgressBar();
            this.updateVideoProgress();
        }, 100);
    }

    stopPlaybackTimer() {
        if (this.playbackTimer) {
            clearInterval(this.playbackTimer);
            this.playbackTimer = null;
        }
        this.saveProgress();
    }

    seekToPosition(event) {
        if (!this.state.currentVideo) return;

        const rect = event.target.getBoundingClientRect();
        const clickX = event.clientX - rect.left;
        const percentage = clickX / rect.width;
        
        const newTime = Math.max(0, Math.min(
            percentage * this.state.currentVideo.duration,
            this.state.currentVideo.duration
        ));
        
        if (this.youtubePlayer) {
            // Use real YouTube player
            this.youtubePlayer.seekTo(newTime);
        } else {
            // Use simulated seeking
            this.state.currentTime = newTime;
            this.updateProgressBar();
            this.updateVideoProgress();
            this.saveProgress();
        }
        
        console.log('Seeked to:', this.formatTime(newTime));
    }

    markCurrentVideoComplete() {
        if (!this.state.currentVideo) return;
        
        console.log('Marking video complete:', this.state.currentVideo.title);
        this.completeCurrentVideo();
        this.selectNextVideo();
    }

    completeCurrentVideo() {
        if (!this.state.currentVideo || !this.state.currentPlaylist) return;

        this.state.currentVideo.completed = true;
        this.state.currentVideo.completionPercentage = 100;
        this.state.currentVideo.currentTime = this.state.currentVideo.duration;
        this.state.currentTime = this.state.currentVideo.duration;
        
        // Save completion to storage
        this.storage.markVideoComplete(this.state.currentPlaylist.id, this.state.currentVideo.id);
        
        this.calculateProgressStats();
        this.updateProgressAnalytics();
        this.updateVideoList();
        this.updateHeaderInfo();

        // Show completion notification
        this.showToast('Video Complete!', `"${this.state.currentVideo.title}" marked as complete`, 'success', 3000);

        console.log('Video completed:', this.state.currentVideo.title);
    }

    selectNextVideo() {
        if (!this.state.currentPlaylist) return;

        const currentIndex = this.state.currentPlaylist.videos.findIndex(
            v => v.id === this.state.currentVideo.id
        );
        
        if (currentIndex < this.state.currentPlaylist.videos.length - 1) {
            this.selectVideo(this.state.currentPlaylist.videos[currentIndex + 1]);
        } else {
            console.log('Reached end of playlist');
        }
    }

    handleVideoSelection(event) {
        const videoItem = event.target.closest('.video-item');
        if (!videoItem) return;

        const videoId = videoItem.dataset.videoId;
        const video = this.state.currentPlaylist.videos.find(v => v.id === videoId);
        
        if (video) {
            this.selectVideo(video);
        }
    }

    filterVideos() {
        const searchInput = document.getElementById('videoSearchInput');
        const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';
        const activeFilterBtn = document.querySelector('.filter-btn.active');
        const activeFilter = activeFilterBtn ? activeFilterBtn.dataset.filter : 'all';
        
        const videoItems = document.querySelectorAll('.video-item');
        
        videoItems.forEach(item => {
            const videoId = item.dataset.videoId;
            const video = this.state.currentPlaylist.videos.find(v => v.id === videoId);
            if (!video) return;
            
            const title = video.title.toLowerCase();
            const matchesSearch = !searchTerm || title.includes(searchTerm);
            const matchesFilter = this.matchesFilter(video, activeFilter);
            
            item.style.display = (matchesSearch && matchesFilter) ? 'flex' : 'none';
        });
    }

    matchesFilter(video, filter) {
        switch (filter) {
            case 'completed':
                return video.completed;
            case 'progress':
                return !video.completed && video.completionPercentage > 0;
            case 'unwatched':
                return video.completionPercentage === 0;
            default:
                return true;
        }
    }

    handleFilterChange(event) {
        document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
        event.target.classList.add('active');
        this.filterVideos();
    }


    calculateProgressStats() {
        // Use real analytics from StorageManager for global stats
        const realAnalytics = this.storage.calculateRealAnalytics();
        
        // For the current playlist's progress, we still need to calculate based on its videos
        let currentPlaylistTotalVideos = 0;
        let currentPlaylistCompletedVideos = 0;
        let currentPlaylistInProgressVideos = 0;
        let currentPlaylistTotalWatchTime = 0;
        let currentPlaylistTotalDuration = 0;

        if (this.state.currentPlaylist && this.state.currentPlaylist.videos) {
            const videos = this.state.currentPlaylist.videos;
            currentPlaylistTotalVideos = videos.length;
            currentPlaylistCompletedVideos = videos.filter(v => v.completed).length;
            currentPlaylistInProgressVideos = videos.filter(v => !v.completed && v.completionPercentage > 0).length;
            currentPlaylistTotalWatchTime = videos.reduce((total, v) => total + (v.currentTime || 0), 0);
            currentPlaylistTotalDuration = videos.reduce((total, v) => total + v.duration, 0);
        }

        this.state.progressStats = {
            totalVideos: currentPlaylistTotalVideos,
            completedVideos: currentPlaylistCompletedVideos,
            inProgressVideos: currentPlaylistInProgressVideos,
            totalWatchTime: currentPlaylistTotalWatchTime,
            totalDuration: currentPlaylistTotalDuration,
            completionPercentage: currentPlaylistTotalDuration > 0 ? (currentPlaylistTotalWatchTime / currentPlaylistTotalDuration) * 100 : 0,
            currentStreak: realAnalytics.currentStreak // Use global streak from StorageManager
        };
    }

    updateProgressAnalytics() {
        this.calculateProgressStats(); // This will update this.state.progressStats
        const stats = this.state.progressStats; // Use the updated stats
        const globalAnalytics = this.storage.calculateRealAnalytics(); // Get global analytics for streak and total watch time

        // Update circular progress
        const progressCircle = document.querySelector('.progress-circle');
        const progressText = document.querySelector('.progress-text');
        
        if (progressCircle && progressText) {
            const percentage = Math.round(stats.completionPercentage);
            progressCircle.style.background = `conic-gradient(var(--color-primary) ${percentage * 3.6}deg, var(--color-secondary) ${percentage * 3.6}deg)`;
            progressText.textContent = `${percentage}%`;
        }

        // Update stats
        const completedCountEl = document.getElementById('completedCount');
        const timeWatchedEl = document.getElementById('timeWatched');
        const currentStreakEl = document.getElementById('currentStreak');
        
        if (completedCountEl) completedCountEl.textContent = `${stats.completedVideos}/${stats.totalVideos}`;
        if (timeWatchedEl) timeWatchedEl.textContent = this.formatTime(globalAnalytics.totalWatchTime); // Use global watch time
        if (currentStreakEl) currentStreakEl.textContent = `${globalAnalytics.currentStreak} days`; // Use global streak

    }


    renderVideoList() {
        const videoList = document.getElementById('videoList');
        if (!videoList || !this.state.currentPlaylist) return;

        videoList.innerHTML = '';

        this.state.currentPlaylist.videos.forEach(video => {
            const videoItem = document.createElement('div');
            videoItem.className = 'video-item';
            videoItem.dataset.videoId = video.id;
            
            if (this.state.currentVideo && this.state.currentVideo.id === video.id) {
                videoItem.classList.add('active');
            }

            const statusIcon = this.getStatusIcon(video);
            
            videoItem.innerHTML = `
                <div class="video-thumbnail">
                    <img src="${video.thumbnail}" alt="Video ${video.position}" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                    <div style="display: none;">${video.position}</div>
                </div>
                <div class="video-details">
                    <h4 class="video-title">${video.title}</h4>
                    <div class="video-meta">
                        <span>${this.formatTime(video.duration)}</span>
                        <div class="video-status">
                            ${statusIcon}
                            <span>${Math.round(video.completionPercentage)}%</span>
                        </div>
                    </div>
                </div>
            `;
            
            videoList.appendChild(videoItem);
        });
    }

    getStatusIcon(video) {
        if (video.completed) {
            return '<div class="status-icon completed"><i class="fas fa-check"></i></div>';
        } else if (video.completionPercentage > 0) {
            return '<div class="status-icon progress"><i class="fas fa-play"></i></div>';
        } else {
            return '<div class="status-icon unwatched"><i class="fas fa-circle"></i></div>';
        }
    }

    updateVideoPlayer() {
        if (!this.state.currentVideo) return;

        const videoPlayer = document.getElementById('videoPlayer');
        const currentVideoTitle = document.getElementById('currentVideoTitle');
        const videoDuration = document.getElementById('videoDuration');
        const videoProgress = document.getElementById('videoProgress');

        // Update video info
        if (currentVideoTitle) currentVideoTitle.textContent = this.state.currentVideo.title;
        if (videoDuration) videoDuration.textContent = `Duration: ${this.formatTime(this.state.currentVideo.duration)}`;
        if (videoProgress) videoProgress.textContent = `Progress: ${Math.round(this.state.currentVideo.completionPercentage)}%`;

        // Update player display
        if (videoPlayer && !this.youtubePlayer) {
            // Determine the appropriate message and actions
            let statusMessage;
            let retryButton = '';

            if (this.isUsingRealAPI) {
                statusMessage = 'Player initialization failed - Using demo mode';
                retryButton = '<button class="btn btn--secondary" onclick="window.playlistTracker.retryPlayerInitialization()" style="margin-top: 8px; font-size: 12px; padding: 4px 8px;">Retry Player</button>';
            } else {
                statusMessage = 'Demo mode - Provide API key for real playback';
            }

            // Show placeholder for simulated player
            videoPlayer.innerHTML = `
                <div class="video-placeholder">
                    <i class="fab fa-youtube"></i>
                    <p>Playing: ${this.state.currentVideo.title}</p>
                    <div class="video-thumbnail" style="margin-top: 16px;">
                        <img src="${this.state.currentVideo.thumbnail}" alt="${this.state.currentVideo.title}" style="width: 120px; height: 68px; border-radius: 8px;">
                    </div>
                    <p style="margin-top: 12px; font-size: 12px; opacity: 0.7;">
                        ${statusMessage}
                    </p>
                    ${retryButton}
                </div>
            `;
        }

        this.updateProgressBar();
    }

    // Retry player initialization
    async retryPlayerInitialization() {
        if (!this.isUsingRealAPI) {
            this.showToast('No API Key', 'Please provide a YouTube API key first', 'warning');
            return;
        }

        this.showToast('Retrying...', 'Attempting to initialize YouTube player', 'info');

        const success = await this.initializeYouTubePlayer();
        if (success) {
            this.showToast('Success!', 'YouTube player initialized successfully', 'success');
            // Reload current video if available
            if (this.state.currentVideo) {
                this.youtubePlayer.loadVideo(this.state.currentVideo.id, this.state.currentTime || 0);
            }
        } else {
            this.showToast('Failed', 'Player initialization failed again', 'error');
        }
    }

    updateVideoList() {
        const videoItems = document.querySelectorAll('.video-item');
        videoItems.forEach(item => {
            item.classList.remove('active');
            if (this.state.currentVideo && item.dataset.videoId === this.state.currentVideo.id) {
                item.classList.add('active');
            }
        });
    }

    updateProgressBar() {
        if (!this.state.currentVideo) return;

        const progressFill = document.getElementById('progressBarFill');
        const progressHandle = document.querySelector('.progress-handle');
        const currentTimeEl = document.getElementById('currentTime');
        const totalTimeEl = document.getElementById('totalTime');

        const percentage = (this.state.currentTime / this.state.currentVideo.duration) * 100;
        
        if (progressFill) progressFill.style.width = `${percentage}%`;
        if (progressHandle) progressHandle.style.left = `${percentage}%`;
        if (currentTimeEl) currentTimeEl.textContent = this.formatTime(this.state.currentTime);
        if (totalTimeEl) totalTimeEl.textContent = this.formatTime(this.state.currentVideo.duration);
    }

    updateVideoProgress() {
        if (!this.state.currentVideo) return;

        this.state.currentVideo.currentTime = this.state.currentTime;
        this.state.currentVideo.completionPercentage = 
            (this.state.currentTime / this.state.currentVideo.duration) * 100;

        const videoProgressEl = document.getElementById('videoProgress');
        if (videoProgressEl) {
            videoProgressEl.textContent = `Progress: ${Math.round(this.state.currentVideo.completionPercentage)}%`;
        }

        // Auto-complete video if watched 95% or more
        if (this.state.currentVideo.completionPercentage >= 95 && !this.state.currentVideo.completed) {
            if (this.state.settings.autoMarkComplete) {
                this.completeCurrentVideo();
            }
        }

        this.calculateProgressStats();
        this.updateProgressAnalytics();
    }

    saveProgress() {
        if (!this.state.currentVideo || !this.state.currentPlaylist) return;
        
        // Update current video progress
        this.updateVideoProgress();
        
        // Save video progress to storage
        const progressData = {
            currentTime: this.state.currentTime,
            completionPercentage: Math.round((this.state.currentTime / this.state.currentVideo.duration) * 100),
            lastWatched: new Date().toISOString()
        };
        
        this.storage.updateVideoProgress(
            this.state.currentPlaylist.id,
            this.state.currentVideo.id,
            progressData
        );
        
        // Update current playlist in storage
        this.storage.setCurrentPlaylist(this.state.currentPlaylist);
        
        console.log('Progress saved for:', this.state.currentVideo.title);
    }

    // Save settings to storage
    saveSettings() {
        this.storage.saveSettings(this.state.settings);
    }

    // Export/Import functionality
    exportData() {
        const data = this.storage.exportData();
        if (!data) {
            this.showToast('Export Failed', 'No data to export', 'error');
            return;
        }

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `youtube-tracker-backup-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showToast('Export Complete', 'Data exported successfully', 'success');
    }

    importData() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const data = JSON.parse(e.target.result);
                    if (this.storage.importData(data)) {
                        this.showToast('Import Complete', 'Data imported successfully', 'success');
                        // Reload the page to reflect changes
                        setTimeout(() => window.location.reload(), 1000);
                    } else {
                        this.showToast('Import Failed', 'Invalid data format', 'error');
                    }
                } catch (error) {
                    this.showToast('Import Failed', 'Invalid JSON file', 'error');
                }
            };
            reader.readAsText(file);
        };
        
        input.click();
    }

    // This method is replaced by saveSettingsFromModal below

    resetSettings() {
        this.state.settings = {
            theme: 'auto',
            autoSave: true,
            notifications: true,
            autoMarkComplete: true
        };

        this.storage.saveSettings(this.state.settings);
        this.updateSettingsForm();
        this.applyTheme(this.state.settings.theme);

        this.showToast('Settings Reset', 'All settings have been reset to defaults', 'info');
        console.log('Settings reset to defaults');
    }

    applyTheme() {
        const theme = this.state.settings.theme;
        const root = document.documentElement;
        
        if (theme === 'auto') {
            root.removeAttribute('data-color-scheme');
        } else {
            root.setAttribute('data-color-scheme', theme);
        }
    }

    updateSettingsForm() {
        const themeSelect = document.getElementById('themeSelect');
        const autoSaveToggle = document.getElementById('autoSaveToggle');
        const autoCompleteToggle = document.getElementById('autoCompleteToggle');
        const notificationsToggle = document.getElementById('notificationsToggle');
        const storageSelect = document.getElementById('storageSelect');

        if (themeSelect) themeSelect.value = this.state.settings.theme;
        if (autoSaveToggle) autoSaveToggle.checked = this.state.settings.autoSave;
        if (autoCompleteToggle) autoCompleteToggle.checked = this.state.settings.autoMarkComplete;
        if (notificationsToggle) notificationsToggle.checked = this.state.settings.notifications;
        if (storageSelect) storageSelect.value = this.state.settings.storagePreference;
    }

    openSettings() {
        const modal = document.getElementById('settingsModal');
        if (modal) {
            this.updateSettingsForm();
            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }
    }

    closeSettings() {
        const modal = document.getElementById('settingsModal');
        if (modal) {
            modal.classList.add('hidden');
            document.body.style.overflow = '';
        }
    }

    handleThemeChange(event) {
        this.state.settings.theme = event.target.value;
        this.applyTheme();
    }

    // Data Export/Import
    exportData() {
        try {
            const exportData = {
                playlist: this.state.currentPlaylist,
                settings: this.state.settings,
                exportDate: new Date().toISOString(),
                version: '1.0'
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `youtube-playlist-progress-${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            
            console.log('Data exported successfully');
        } catch (error) {
            console.error('Failed to export data:', error);
            alert('Failed to export data. Please try again.');
        }
    }

    importData() {
        try {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            
            input.onchange = (event) => {
                const file = event.target.files[0];
                if (!file) return;
                
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const importData = JSON.parse(e.target.result);
                        
                        if (importData.playlist) {
                            this.state.currentPlaylist = importData.playlist;
                        }
                        if (importData.settings) {
                            this.state.settings = { ...this.state.settings, ...importData.settings };
                        }
                        this.saveProgress();
                        this.updateUI();
                        
                        console.log('Data imported successfully');
                        alert('Data imported successfully!');
                    } catch (error) {
                        console.error('Failed to parse import file:', error);
                        alert('Invalid file format. Please select a valid export file.');
                    }
                };
                reader.readAsText(file);
            };
            
            input.click();
        } catch (error) {
            console.error('Failed to import data:', error);
            alert('Failed to import data. Please try again.');
        }
    }

    // Keyboard Shortcuts
    handleKeyboardShortcuts(event) {
        // Only handle shortcuts when not typing in inputs
        if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
            return;
        }

        switch (event.key) {
            case ' ':
                event.preventDefault();
                this.togglePlayback();
                break;
            case 'ArrowLeft':
                event.preventDefault();
                this.seekRelative(-10);
                break;
            case 'ArrowRight':
                event.preventDefault();
                this.seekRelative(10);
                break;
            case 'ArrowUp':
                event.preventDefault();
                this.selectPreviousVideo();
                break;
            case 'ArrowDown':
                event.preventDefault();
                this.selectNextVideo();
                break;
            case 'Enter':
                if (this.state.currentVideo) {
                    event.preventDefault();
                    this.markCurrentVideoComplete();
                }
                break;
            case 'Escape':
                this.closeSettings();
                break;
        }
    }

    seekRelative(seconds) {
        if (!this.state.currentVideo) return;
        
        this.state.currentTime = Math.max(0, Math.min(
            this.state.currentTime + seconds,
            this.state.currentVideo.duration
        ));
        
        this.updateProgressBar();
        this.updateVideoProgress();
        this.saveProgress();
    }

    selectPreviousVideo() {
        if (!this.state.currentPlaylist || !this.state.currentVideo) return;

        const currentIndex = this.state.currentPlaylist.videos.findIndex(
            v => v.id === this.state.currentVideo.id
        );
        
        if (currentIndex > 0) {
            this.selectVideo(this.state.currentPlaylist.videos[currentIndex - 1]);
        }
    }

    selectNextVideo() {
        if (!this.state.currentPlaylist || !this.state.currentVideo) return;

        const currentIndex = this.state.currentPlaylist.videos.findIndex(
            v => v.id === this.state.currentVideo.id
        );
        
        if (currentIndex < this.state.currentPlaylist.videos.length - 1) {
            this.selectVideo(this.state.currentPlaylist.videos[currentIndex + 1]);
        }
    }

    // Utility Functions
    formatTime(seconds) {
        if (!seconds || seconds < 0) return '0:00';
        
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        
        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }
    }

    updateHeaderInfo() {
        if (!this.state.currentPlaylist) return;

        const titleEl = document.getElementById('currentPlaylistTitle');
        const statsEl = document.getElementById('globalStats');
        
        if (titleEl) {
            titleEl.textContent = this.state.currentPlaylist.title;
        }
        
        if (statsEl) {
            const stats = this.state.progressStats;
            statsEl.textContent = `${stats.completedVideos}/${stats.totalVideos} videos • ${Math.round(stats.completionPercentage)}% complete`;
        }
    }

    updateUI() {
        if (this.state.currentPlaylist) {
            // Show main content
            const uploadSection = document.getElementById('uploadSection');
            const mainContent = document.getElementById('mainContent');
            
            if (uploadSection && mainContent) {
                uploadSection.style.display = 'none';
                mainContent.style.display = 'grid';
            }
            
            this.renderVideoList();
            this.updateProgressAnalytics();
            this.updateHeaderInfo();

            if (this.state.currentVideo) {
                this.updateVideoPlayer();
            }
        }
    }

    // Initialize with stored data
    async init() {
        console.log('Initializing PlaylistTracker...');
        
        // Ensure loading indicator is hidden immediately
        this.hideLoading();
        
        this.loadSettings();
        await this.loadFromStorage();
        this.bindEvents();
        this.updateUI();
        
        // Initialize YouTube API if key is available
        if (window.CONFIG && window.CONFIG.YOUTUBE_API_KEY && window.CONFIG.YOUTUBE_API_KEY !== 'YOUR_API_KEY_HERE') {
            await this.initializeYouTubeAPI(window.CONFIG.YOUTUBE_API_KEY);
        }
        
        // Double-check loading indicator is hidden after initialization
        this.hideLoading();
        
        // Show welcome message
        if (!this.state.currentPlaylist) {
            setTimeout(() => {
                this.showToast(
                    'Welcome!',
                    'Go to the dashboard to add playlists and start tracking your progress',
                    'info',
                    8000
                );
            }, 1000);
        }
    }

    // Load settings from storage
    loadSettings() {
        const savedSettings = this.storage.getSettings();
        this.state.settings = { ...this.state.settings, ...savedSettings };
        
        // Apply theme
        this.applyTheme(this.state.settings.theme);
    }

    // Load data from storage
    async loadFromStorage() {
        try {
            // Load current playlist from storage
            const currentPlaylist = this.storage.getCurrentPlaylist();
            if (currentPlaylist) {
                this.state.currentPlaylist = currentPlaylist;
                
                // Show main content
                const uploadSection = document.getElementById('uploadSection');
                const mainContent = document.getElementById('mainContent');
                
                if (uploadSection && mainContent) {
                    uploadSection.style.display = 'none';
                    mainContent.style.display = 'grid';
                }
                
                this.calculateProgressStats();
                this.renderVideoList();
                this.updateProgressAnalytics();
                this.updateHeaderInfo();
                
                // Load first incomplete video or first video
                const resumeVideo = currentPlaylist.videos.find(v => 
                    v.completionPercentage > 0 && !v.completed
                ) || currentPlaylist.videos[0];
                
                if (resumeVideo) {
                    this.selectVideo(resumeVideo);
                }
            }
        } catch (error) {
            console.error('Error loading from storage:', error);
        }
    }

    // Apply theme
    applyTheme(theme) {
        const body = document.body;
        body.classList.remove('theme-light', 'theme-dark');
        
        if (theme === 'dark') {
            body.classList.add('theme-dark');
        } else if (theme === 'light') {
            body.classList.add('theme-light');
        } else {
            // Auto theme - use system preference
            if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                body.classList.add('theme-dark');
            } else {
                body.classList.add('theme-light');
            }
        }
    }

    // Update UI elements
    updateUI() {
        // Update settings UI
        const themeSelect = document.getElementById('themeSelect');
        const autoSaveToggle = document.getElementById('autoSaveToggle');
        const autoCompleteToggle = document.getElementById('autoCompleteToggle');
        const notificationsToggle = document.getElementById('notificationsToggle');
        
        if (themeSelect) themeSelect.value = this.state.settings.theme;
        if (autoSaveToggle) autoSaveToggle.checked = this.state.settings.autoSave;
        if (autoCompleteToggle) autoCompleteToggle.checked = this.state.settings.autoMarkComplete;
        if (notificationsToggle) notificationsToggle.checked = this.state.settings.notifications;
    }

}

// Create global instance
window.playlistTracker = new PlaylistTracker();
window.app = window.playlistTracker; // Alias for easier access
